#include "sm_result.h"
#include "sm_ready.h"
#include "algo.h"
#include "power.h"
#include "main.h"
#include "ST7789.h"
#include "key.h"


volatile static uint32_t sm6_tick_ms;




void sm_result_init(void)
{
    sm6_tick_ms = 0;
//    uint32_t bilirubin = bilirubin_average;
//    page_result(bilirubin);
}


void sm_result_tick(void)
{
    sm6_tick_ms++;
}


void sm_result_event(ui_evt_t ui_evt)
{
    //用户按键
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
        && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER)) {
        if (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_RELEASED) { //单击
            //多次求均值模式
//			sm_ready_average_mode();
            sm_jump(SM_READY, 0);
        }
        if (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) { //长按
            if (ui_evt.evt.ui_evt_key.key_pressed_ms >= 3000) {
                // 长按关机 - 使用SYSMC控制
                nrf_gpio_pin_clear(GPIO_LCD_BK); // 关闭背光
                close_LCD(); // 关屏
                nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
                nrf_gpio_cfg_sense_input(BQ24075_PG_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
                sysmc_power_off(); // 关闭系统主电源
                sd_power_system_off();
            }
        }
    }
    //按压探头
    else if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
             && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)
             && (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED)) {
//				if (get_battery_level() == BATTERY_LEVEL_EMPTY)//没电了   //Divid  ++++   测量前看下电量
//				{
//						sm_jump(SM_POWER_OFF, 1);//自动关机
//						return;
//				}
        //单次模式
//		sm_ready_single_mode();
//		nrf_delay_ms(20);
        //测量
        sm_jump(SM_MEASURE, 0);
    }
}


