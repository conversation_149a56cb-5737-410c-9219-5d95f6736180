#include "power.h"
#include "nrf_gpio.h"
#include "nrf_drv_gpiote.h"
#include "nrf_drv_power.h"
#include "app_timer.h"
#include "gui.h"
#include "sm.h"
#include "sm_power_on.h"
#include "pwm.h"

/**LGS4056H充电管理IC**/
static charge_state_t sampled = CHARGE_STATE_IDLE;
static charge_state_t last_stable_state = CHARGE_STATE_IDLE;
static bool state_change_pending = false;

static void bq24075_update_state(charge_state_t new_state);
static void bq24075_gpio_event_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action);
void bq24075_read_and_update_state(void);

void bq24075_gpio_init(void)
{
    ret_code_t err_code;

    // 配置PG引脚中断 (Power Good信号)
    nrf_drv_gpiote_in_config_t pg_config = GPIOTE_CONFIG_IN_SENSE_TOGGLE(true);
    pg_config.pull = NRF_GPIO_PIN_PULLUP;

    err_code = nrf_drv_gpiote_in_init(BQ24075_PG_PIN, &pg_config, bq24075_gpio_event_handler);
    APP_ERROR_CHECK(err_code);

    nrf_drv_gpiote_in_event_enable(BQ24075_PG_PIN, true);

    // 配置CHG引脚中断 (Charge状态信号)
    nrf_drv_gpiote_in_config_t chg_config = GPIOTE_CONFIG_IN_SENSE_TOGGLE(true);
    chg_config.pull = NRF_GPIO_PIN_PULLUP;

    err_code = nrf_drv_gpiote_in_init(BQ24075_CHG_PIN, &chg_config, bq24075_gpio_event_handler);
    APP_ERROR_CHECK(err_code);

    nrf_drv_gpiote_in_event_enable(BQ24075_CHG_PIN, true);
}


// GPIO中断事件处理函数
static void bq24075_gpio_event_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    // 不管是哪个引脚触发，都重新读取两个引脚的状态
    bq24075_read_and_update_state();
}

// 读取引脚状态并更新充电状态
void bq24075_read_and_update_state(void)
{
    bool pg = nrf_gpio_pin_read(BQ24075_PG_PIN);    // 0 = 有电源输入 (Power Good)
    bool chg = nrf_gpio_pin_read(BQ24075_CHG_PIN);  // 0 = 正在充电, 1 = 充满/高阻

    charge_state_t new_state;

    // BQ24075状态判断逻辑：
    // PG=0 且 CHG=0：正在充电
    // PG=0 且 CHG=1：充满电
    // PG=1：未充电
    if (pg == 0) {  // 有电源输入
        if (chg == 0) {
            // PG=0 且 CHG=0：正在充电
            blue_light();
            new_state = CHARGE_STATE_CHARGING;
        } else {
            // PG=0 且 CHG=1(高阻)：充满电
            write_light();
            new_state = CHARGE_STATE_CHARGED;
        }
    } else {
        // PG=1：无电源输入，未充电
        close_light();
        new_state = CHARGE_STATE_IDLE;
    }

    // 只有状态真正改变时才标记需要跳转
    if (new_state != last_stable_state) {
        sampled = new_state;
        last_stable_state = new_state;
        state_change_pending = true;
        NRF_LOG_INFO("BQ24075 charge state changed to: %d (PG=%d, CHG=%d)", new_state, pg, chg);
    }
}


// 充电状态获取
charge_state_t get_charg_sta(void){
    return sampled;
}

extern volatile bool no_return;	
// 处理待处理的状态跳转 - 在主循环中调用
void bq24075_process_state_change(void)
{
    if (state_change_pending) {
        state_change_pending = false;

        // 获取当前状态机状态
        sm_t current_sm = sm_get();

        switch (sampled) {
            case CHARGE_STATE_CHARGING:
            case CHARGE_STATE_CHARGED:
                // 插入充电器，自动开机并跳转到充电界面
                sysmc_power_on();
                sm_jump(SM_CHARG, 0);
                break;
            case CHARGE_STATE_IDLE:
                // 只有当前在充电状态时，拔掉充电器才点亮背光并跳转到主界面
                if ((current_sm == SM_CHARG) && (no_return == 0)) {
                    nrf_gpio_pin_set(GPIO_LCD_BK); // 点亮背光

					//判断电池和加锁状态，避免充电绕过检测
					check_power_on();
					go_lowB_loc();
                }
                break;
        }
    }
}

// 电量显示
void format_percentage(int value, char *output_buffer, size_t buffer_size)
{
    // 限制范围在 0~100
    if (value < 0) value = 0;
    if (value > 100) value = 100;

    snprintf(output_buffer, buffer_size, "%d%%", value);
}

// 更新充电界面的电量显示
void update_charged_screen_battery_level(void) {
    lv_ui ui = get_ui();
    extern uint8_t get_SOC(void);  // 从CW2015获取电量

    // 获取当前电量百分比
    uint8_t battery_percent = get_SOC();

    // 格式化电量文字
    char battery_text[16];
    format_percentage(battery_percent, battery_text, sizeof(battery_text));

    // 更新充电界面的标签文字
    if (ui.charged_label_1 != NULL) {
		if(get_charg_sta() == CHARGE_STATE_CHARGING){
			lv_obj_set_style_text_font(ui.charged_label_1, &lv_PingFang_regular_18, LV_PART_MAIN);
			lv_label_set_text(ui.charged_label_1, battery_text);
		}else if(get_charg_sta() == CHARGE_STATE_CHARGED){
			lv_obj_set_style_text_font(ui.charged_label_1, &lv_PingFang_regular_18, LV_PART_MAIN|LV_STATE_DEFAULT);
			lv_label_set_text(ui.charged_label_1, "充电完成");
		}
        lv_obj_invalidate(ui.charged_label_1);  // 强制刷新显示
    }
}
