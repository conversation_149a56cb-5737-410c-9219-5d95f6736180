/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "sm.h"
#include "gui.h"
#include "battery_adc.h"

/**
 * @brief 创建错误界面的所有标签
 * @param ui 界面结构体指针
 */
static void create_all_labels(lv_ui *ui)
{
    //Write codes error_label_1 - 数字显示标签（使用Pingfang58字体，坐标106,61）
    ui->error_label_1 = lv_label_create(ui->error);
    lv_label_set_text(ui->error_label_1, "");
    lv_label_set_long_mode(ui->error_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->error_label_1, 106, 61);
    lv_obj_set_size(ui->error_label_1, 108, 60);

    //Write style for error_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->error_label_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->error_label_1, &lv_PingFang_regular_50, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->error_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->error_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->error_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes error_label_2 - 中文字体预留位置标签（坐标77,121）
    ui->error_label_2 = lv_label_create(ui->error);
    lv_label_set_text(ui->error_label_2, "");
    lv_label_set_long_mode(ui->error_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->error_label_2, 77, 121);
    lv_obj_set_size(ui->error_label_2, 165, 28);

    //Write style for error_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->error_label_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->error_label_2, &lv_PingFang_regular_20, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->error_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->error_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->error_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
}

/**
 * @brief 创建错误界面的所有图片
 * @param ui 界面结构体指针
 */
static void create_all_images(lv_ui *ui)
{	
	ui->error_img_1 = lv_img_create(ui->error);
    lv_obj_add_flag(ui->error_img_1, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->error_img_1, &_E01_alpha_43x13);
    lv_img_set_pivot(ui->error_img_1, 50,50);
    lv_img_set_angle(ui->error_img_1, 0);
    lv_obj_set_pos(ui->error_img_1, 25, 41);
    lv_obj_set_size(ui->error_img_1, 43, 13);

    lv_obj_set_style_img_recolor_opa(ui->error_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->error_img_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->error_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->error_img_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
}

/**
 * @brief 创建错误界面的所有自定义组件
 * @param ui 界面结构体指针
 */
static void create_all_custom_widgets(lv_ui *ui)
{
    // 创建电池电量显示
    battery_create(ui->error);
    
    // 创建蓝牙图标
    bluetooth_image_create(ui->error);
    
    // 创建测量次数图标（默认显示1次）
    number_square_create(ui->error, 1);
}

/**
 * @brief 配置错误界面的显示模式
 * @param ui 界面结构体指针
 */
static void configure_display_mode(lv_ui *ui)
{
    // 配置电池和蓝牙状态
    battery_set_level(get_SOC());
    
    extern bool ble_conn_is_connected(void);
    if(ble_conn_is_connected() != true) {
        bluetooth_image_set_visible(false);
    } else {
        bluetooth_image_set_visible(true);
    }
}

/**
 * @brief 设置错误界面的主要函数
 * @param ui 界面结构体指针
 */
void setup_scr_error(lv_ui *ui)
{
    // 1. 创建主屏幕容器，初始设为隐藏状态
    ui->error = lv_obj_create(NULL);
    lv_obj_set_size(ui->error, 320, 170);
    lv_obj_set_scrollbar_mode(ui->error, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_flag(ui->error, LV_OBJ_FLAG_HIDDEN); // 隐藏整个屏幕
    
    // 2. 暂时禁用自动布局更新
    lv_obj_add_flag(ui->error, LV_OBJ_FLAG_LAYOUT_1);

    //Write style for error, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->error, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->error, lv_color_hex(0x030303), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->error, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 3. 批量创建所有基础组件
    create_all_labels(ui);
    create_all_images(ui);
    create_all_custom_widgets(ui);
    
    // 4. 在所有组件创建完成后，再进行条件性的显示/隐藏设置
    configure_display_mode(ui);
    
    // 5. 恢复布局并更新
    lv_obj_clear_flag(ui->error, LV_OBJ_FLAG_LAYOUT_1);
    lv_obj_update_layout(ui->error);
    
    // 6. 一次性显示整个界面
    lv_obj_clear_flag(ui->error, LV_OBJ_FLAG_HIDDEN);
}
