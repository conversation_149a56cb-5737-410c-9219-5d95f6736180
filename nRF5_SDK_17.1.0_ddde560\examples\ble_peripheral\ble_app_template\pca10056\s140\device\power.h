#ifndef __POWER_H__
#define __POWER_H__


#include <stdint.h>
#include <string.h>

#include "nordic_common.h"
#include "nrf.h"
#include "nrf_assert.h"
#include "main.h"

#include "nrf_delay.h"
#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

typedef enum {
    CHARGE_STATE_IDLE,
    CHARGE_STATE_CHARGING,
    CHARGE_STATE_CHARGED
} charge_state_t;


void bq24075_gpio_init(void);
void bq24075_read_and_update_state(void);
void bq24075_process_state_change(void);
charge_state_t get_charg_sta(void);
void format_percentage(int value, char *output_buffer, size_t buffer_size);
void update_charged_screen_battery_level(void);

#endif
