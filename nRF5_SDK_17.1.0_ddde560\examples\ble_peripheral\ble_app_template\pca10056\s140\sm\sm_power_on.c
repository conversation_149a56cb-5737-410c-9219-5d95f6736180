#include "sm_power_on.h"
#include "nrf_drv_gpiote.h"
#include "nrf_pwr_mgmt.h"
#include "sm.h"
#include "gui.h"
#include "battery_adc.h"
#include "power.h"
#include "bm8563.h"
#include "spiflash20.h"

#define POWER_ON_TIME 3000		//开机动画时间
#define POWER_BARR		20			//电池电量过低提示
#define low_check 0

volatile static uint32_t sm_on_tick_ms;		//开机计时器
static uint32_t is_lock;													//设备加锁状态
static uint8_t autolock;					//设备自动加锁（0解除，1打开）
static bool arrive_stamp;					//是否到期  1是  0否
uint8_t soc;
static bool low_soc;

void sm_power_on_init(void)
{
    //开机动画
    switch_to_next_screen(UI_SCREEN_LOG);
	
	//判断电池和加锁状态
	check_power_on();
	
	//检查是否自动加锁并且到期
	check_autolock_stamp();
	
	//开机检测是否在充电
    bq24075_read_and_update_state();
}


void sm_power_on_tick(void)
{
    sm_on_tick_ms++;
    if(sm_on_tick_ms >= POWER_ON_TIME) {
        sm_on_tick_ms = 0;
        go_lowB_loc();

    }
}


void sm_power_on_event(ui_evt_t ui_evt)
{
}

//检测电池电量和加锁状态
void check_power_on(void){
	//检查电池电量
	cw2015_read_soc_percent();
	if(get_SOC() < POWER_BARR) {
        low_soc = true;
    }
	 //检查设备加锁状态
	ext_lock_read(&is_lock);
	NRF_LOG_INFO("is_lock :%d",is_lock);
}

//进入低电量或者加锁状态
void go_lowB_loc(void){
        if(low_soc == true) {
			if(get_charg_sta() == CHARGE_STATE_CHARGING){
				sm_jump(SM_CHARG,0);
			}
            else sm_jump(SM_LOWSOC,0);
        } else {
            if(is_lock == 0 || (autolock && arrive_stamp)) {
                sm_jump(SM_LOCK,0);
            } else {
                if(get_charg_sta() == CHARGE_STATE_IDLE) {
					sm_jump(SM_READY,0);
                } else sm_jump(SM_CHARG,0);
            }
        }
}

//检查是否自动加锁并且到期
void check_autolock_stamp(void){
	ext_autolock_read(&autolock);
	
	//判断是否到期
	uint32_t stamp0,stamp1;
	//rtc
    stamp0 = GetTimeStamp();
    ext_stamp_read(&stamp1);
    if(stamp1 <= stamp0 - (8 * 3600)){
		arrive_stamp = 1;
		ext_lock_write(1);
	}else{
		arrive_stamp = 0;
	}
}