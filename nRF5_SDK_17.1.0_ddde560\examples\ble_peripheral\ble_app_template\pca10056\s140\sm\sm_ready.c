#include "sm_ready.h"
#include "frame.h"
#include "sm_history.h"
#include "hide_trig.h"
#include "power.h"
#include "storage.h"
#include "utils.h"
#include "algo.h"
#include "motor.h"
#include "boards.h"
#include "key.h"
#include "ST7789.h"
#include "gui.h"
#include "battery_adc.h"
#include "sm_measure.h"
#include "sm_measure_mode.h"
#include "sm_ble.h"
#include "sm_power_on.h"
#include "spiflash20.h"
#include "app_timer.h"
#include "main.h"
#include "app_scheduler.h"

// 测量界面的松手标志（来自 generated/setup_scr_measure.c）
extern volatile bool stop_flag;

volatile static uint32_t sm8_tick_ms;
volatile static uint32_t SOC_tick_ms;
volatile static uint32_t save_tick_ms;
//三次测量取均值
uint32_t average_times_total = 3;//表示通过几次测量计算均值
uint32_t average_times_now = 0;//目前已经得到几次数据
uint32_t bilirubin_average = 0;//用于求平均值

// 分离的测量结果存储
uint32_t single_measurement_result = 0;  // 单次测量结果
float average_measurement_results[3] = {0, 0, 0};  // 平均值模式下的三次测量结果
uint32_t average_final_result = 0;  // 平均值模式的最终结果
bool average_results_displayed[3] = {false, false, false};  // 跟踪哪些结果已经显示过

uint32_t measure_model_set = 0;//用于缓存APP设置的测量模式，因为正在测量中时不能直接改变
static uint32_t is_sn_showed = 0;//是否已经显示了序列号

//是否测量成功/是否保存
extern volatile bool is_save;

//上一次的测量值
static float last_measure = 0;
//当前值
static float current_measure = 0;

//测量数据
meas_record_t M;

//标记是从测量界面过来的
bool measure_flag;

// 异步保存测量数据，避免与GUI动画/显示冲突
APP_TIMER_DEF(s_save_timer_id);
static bool s_save_timer_created = false;
static volatile bool s_save_pending = false;
static meas_record_t s_pending_record;
static bool s_reset_average_after_save = false;
// READY 界面稳定锁，不允许界面跳转
static volatile uint32_t s_ready_ui_lock_ms = 0;

extern bool oncemeasure_flag;

static void do_flash_save(void * p_event_data, uint16_t size)
{
    (void)p_event_data; (void)size;
	send_measure_data();
    meas_data_write_cyclic(&s_pending_record);
    if (s_reset_average_after_save) {
        bilirubin_average = 0;
        s_reset_average_after_save = false;
    }
	oncemeasure_flag = 1;
    s_save_pending = false;
    is_save = 0;
}

static void save_timer_handler(void * p_context)
{
    (void)p_context;
    // 在调度器中执行耗时保存，避免在中断上下文阻塞
    app_sched_event_put(NULL, 0, do_flash_save);
}

static void schedule_measure_save(bool reset_average)
{
    if (!s_save_timer_created) {
        ret_code_t err_code = app_timer_create(&s_save_timer_id, APP_TIMER_MODE_SINGLE_SHOT, save_timer_handler);
        APP_ERROR_CHECK(err_code);
        s_save_timer_created = true;
    }
    if (s_save_pending) {
        return; // 已有一次保存排队
    }
    s_pending_record = get_measure_data();
    s_reset_average_after_save = reset_average;
    s_save_pending = true;
    // 延后执行，避开动画
    ret_code_t err = app_timer_start(s_save_timer_id, APP_TIMER_TICKS(600), NULL);
    APP_ERROR_CHECK(err);
}

//界面稳定锁
static bool ready_can_navigate(void)
{
    return (s_ready_ui_lock_ms == 0) && (!s_save_pending);
}

static void ready_ui_lock_start(uint32_t lock_ms)
{
    s_ready_ui_lock_ms = lock_ms;
}

void sm_ready_init(void)
{
    sm8_tick_ms = 0;
	save_tick_ms = 0;
	
	// 清理潜在残留的松手标志，避免上一状态临界期影响本状态
    stop_flag = 0;

    //开启关机定时器
    sm_open_timer();
	
	cw2015_read_soc_percent();
	
    //进入主界面
    switch_to_next_screen(UI_SCREEN_RESULT1);
	
	ready_ui_lock_start(800);
	
	// 更新单次测量模式下的日期显示
    update_single_mode_date_display();
	
	//显示蓝牙连接标识
	if(get_ble_con() == 1){
        bluetooth_image_set_visible(true);
	}else  bluetooth_image_set_visible(false);
	
	if(measure_flag){
		average_times_now++;
		current_measure = get_result();
	}else{ 
		current_measure = 0;
	}
    // 显示测量结果
    if (get_measure_mode() == SINGLE_MODE) {
		//当前值大于上次测量的值
		if(current_measure > last_measure){
			show_prepare_line(LINE_TYPE_UP);
		}else if(current_measure < last_measure){
			show_prepare_line(LINE_TYPE_DOWN);
		}else show_prepare_line(LINE_TYPE_HORIZON);
        if (current_measure > 0.0f) {
			if(get_measure_unit() == RESULT1M_MODE){
				show_bilirubin(get_result() * 17.1f);
			}else show_bilirubin(get_result());
			//保存数据
			if(is_save){
				schedule_measure_save(false);
			}
			last_measure = current_measure;
        } else {
            show_bilirubin(0.0f);
        }
    } else {
		//判断average_final_result有无值，无值显示平衡蓝条
		if(average_final_result == 0){
			show_prepare_line(LINE_TYPE_HORIZON);
		}
		
        // 在平均值模式下，如果还没有开始测量或者已经完成所有测量，则重置显示标志
        // 平均值测量模式：检查是否有最终结果
        if (average_times_now == average_times_total && average_final_result > 0) {
            // 有最终平均值，显示在下方
            clear_average_bottom_lines();
            update_average_display();
			
			current_measure = average_final_result;
			//当前值大于上次测量的值
			if(current_measure > last_measure){
				show_prepare_line(LINE_TYPE_UP);
			}else if(current_measure < last_measure){
				show_prepare_line(LINE_TYPE_DOWN);
			}else show_prepare_line(LINE_TYPE_HORIZON);
			
			//保存数据
			if(is_save){
				schedule_measure_save(true);
			}
			last_measure = current_measure;
        } else {
            // 没有最终结果，显示下划线
            show_bilirubin(0.0f);
        }
        // 显示已完成但未显示的测量结果
        for (int i = 0; i < average_times_now; i++) {
            if (!average_results_displayed[i]) {
                float display_value = average_measurement_results[i];
                if (display_value > 0) {  // 只显示有效的测量结果
                    show_average_measurement_result(i, display_value);
                }
                average_results_displayed[i] = true;
            }
        }
		
    }
	if(measure_flag){
		//显示之后
		average_final_result = 0;
		 // 增加测量计数器
		measure_flag = 0;
	}else{
		for (int i = 0; i < 3; i++) {
            average_results_displayed[i] = false;
            average_measurement_results[i] = 0;
        }
        average_times_now = 0;
	}
}


void sm_ready_tick(void)
{
	if(is_save)	save_tick_ms++;
    sm8_tick_ms++;
    SOC_tick_ms++;
	if (s_ready_ui_lock_ms > 0) {
        s_ready_ui_lock_ms--;
    }
    if(SOC_tick_ms >= 5000) {
		is_save = 0;
        SOC_tick_ms = 0;
        battery_set_level(get_SOC());
    }
}

void sm_ready_event(ui_evt_t ui_evt)
{

    //探头按键
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)) {
        if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_DOWN) {
			// 动画期间禁止进入测量，待动画结束再允许
            if(gui_is_line_animating()) {
                return;
            }
            //按下进入测量模式
            sm_jump(SM_MEASURE,0);
        }
    }
    //UP按键
    else if((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_UP)) {
        if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED) {
			if(!ready_can_navigate()) {
                return;
            }
            //UP键短按
			sm_jump(SM_HISTORY,0);
        }
    }
    //DOWN按键

    //用户按键
    else if((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER)) {
        if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) {
            //用户键长按关机 - 检查长按时间
            if (ui_evt.evt.ui_evt_key.key_pressed_ms >= 3000) {
                // 长按关机 - 使用SYSMC控制
                nrf_gpio_pin_clear(GPIO_LCD_BK); // 关闭背光
                close_LCD(); // 关屏
                nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
                nrf_gpio_cfg_sense_input(BQ24075_PG_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
                sysmc_power_off(); // 关闭系统主电源
                sd_power_system_off();
            }
        } else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED) {
			if(!ready_can_navigate()) {
                return;
            }
            //用户键短按切换测量模式
            sm_jump(SM_MEASURE_MODE,0);
        } else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_DOUBLE_CLICK) {
			if(!ready_can_navigate()) {
                return;
            }
            //用户双击
            if(get_measure_unit() == RESULT1_MODE) {
				//设置模式
				set_measure_unit(RESULT1M_MODE);
				//界面切换
				change_unit(RESULT1M_MODE);
			} else {
				set_measure_unit(RESULT1_MODE);
				change_unit(RESULT1_MODE);
			}
        }
    }

}
