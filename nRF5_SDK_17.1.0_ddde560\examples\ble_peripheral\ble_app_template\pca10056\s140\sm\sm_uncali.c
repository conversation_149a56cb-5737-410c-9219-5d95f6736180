#include "sm_uncali.h"
#include "hide_trig.h"
#include "main.h"
#include "ST7789.h"
#include "key.h"



volatile static uint32_t sm3_tick_ms;




void sm_uncali_init(void)
{
    sm3_tick_ms = 0;

//    page_uncali();
}


void sm_uncali_tick(void)
{
    sm3_tick_ms++;
}


void sm_uncali_event(ui_evt_t ui_evt)
{
    //这个页面什么都不做，只接受隐藏按键触发的校准
    //用户按键
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
        && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER)) {
//        hide_trig_input(ui_evt);
        if (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) { //长按
            if (ui_evt.evt.ui_evt_key.key_pressed_ms >= 3000) {
                // 长按关机 - 使用SYSMC控制
                nrf_gpio_pin_clear(GPIO_LCD_BK); // 关闭背光
                close_LCD(); // 关屏
                nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
                nrf_gpio_cfg_sense_input(BQ24075_PG_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
                sysmc_power_off(); // 关闭系统主电源
                sd_power_system_off();
            }
        }
    }
#if 0
    //隐藏关卡判断
    else if (ui_evt.ui_evt_type == UI_EVT_TYPE_HIDE_TRIG) {
        //1号指令：校准方法
        if (ui_evt.evt.ui_evt_hide_trig.command == 1) {
            sm_jump(SM_CALIBRATE, 2);
        }
        //3号指令：特殊校准
        else if (ui_evt.evt.ui_evt_hide_trig.command == 3) {
            sm_jump(SM_CALIBRATE, 3);
        }
    }
#endif
}


