#ifndef __BATTERY_ADC_NEW_H__
#define __BATTERY_ADC_NEW_H__

#include "main.h"
#include <stdint.h>
#include <stdbool.h>

// ==================== 硬件配置 ====================

// ADC配置 - P0.3引脚
#define BATTERY_ADC_PIN             NRF_SAADC_INPUT_AIN1    // P0.3对应AIN1
#define BATTERY_ADC_CHANNEL         1                        // 使用SAADC通道1
#define BATTERY_ADC_RESOLUTION      NRF_SAADC_RESOLUTION_12BIT  // 12位分辨率
#define BATTERY_ADC_GAIN            NRF_SAADC_GAIN1_6        // 1/6增益，测量范围0-3.6V
#define BATTERY_ADC_REFERENCE       NRF_SAADC_REFERENCE_INTERNAL  // 内部参考电压0.6V
#define BATTERY_ADC_ACQTIME         NRF_SAADC_ACQTIME_40US   // 采集时间40us

// 电池电压范围定义 (mV)
#define BATTERY_VOLTAGE_MAX_MV      4200    // 满电电压 4.2V
#define BATTERY_VOLTAGE_MIN_MV      3000    // 截止电压 3.0V
#define BATTERY_VOLTAGE_NOMINAL_MV  3700    // 标称电压 3.7V

// 电量阈值定义
#define BATTERY_LEVEL_FULL          100     // 满电
#define BATTERY_LEVEL_HIGH          80      // 高电量
#define BATTERY_LEVEL_MEDIUM        50      // 中等电量
#define BATTERY_LEVEL_LOW           20      // 低电量阈值
#define BATTERY_LEVEL_CRITICAL      10      // 极低电量阈值
#define BATTERY_LEVEL_EMPTY         0       // 空电

// 测量参数
#define BATTERY_SAMPLE_COUNT        5       // 单次测量采样次数
#define BATTERY_FILTER_SIZE         8       // 移动平均滤波窗口
#define BATTERY_UPDATE_INTERVAL_MS  5000    // 定时更新间隔 (5秒)

// 校准参数默认值
#define BATTERY_CALIBRATION_OFFSET  0       // 校准偏移量 (mV)
#define BATTERY_CALIBRATION_GAIN    1000    // 校准增益 (千分比，1000=100%)

// ==================== 数据结构定义 ====================

/**
 * @brief 电池状态枚举
 */
typedef enum {
    BATTERY_STATUS_UNKNOWN = 0,     // 未知状态
    BATTERY_STATUS_EMPTY,           // 空电 (0%)
    BATTERY_STATUS_CRITICAL,        // 极低电量 (<10%)
    BATTERY_STATUS_LOW,             // 低电量 (10-20%)
    BATTERY_STATUS_MEDIUM,          // 中等电量 (20-60%)
    BATTERY_STATUS_HIGH,            // 高电量 (60-90%)
    BATTERY_STATUS_FULL             // 满电 (>90%)
} battery_status_t;

/**
 * @brief 电池信息结构体
 */
typedef struct {
    uint16_t voltage_raw_mv;        // 原始电压 (mV)
    uint16_t voltage_filtered_mv;   // 滤波后电压 (mV)
    uint8_t level_percent;          // 电量百分比 (0-100)
    battery_status_t status;        // 电池状态
    bool is_valid;                  // 数据是否有效
    uint32_t timestamp;             // 时间戳
} battery_adc_info_t;

// ==================== 核心接口 ====================

/**
 * @brief 初始化电池ADC模块
 * 
 * @return ret_code_t 返回码
 */
ret_code_t battery_adc_init(void);

/**
 * @brief 反初始化电池ADC模块
 */
void battery_adc_deinit(void);

/**
 * @brief 获取完整的电池信息
 * 
 * @param info 电池信息结构体指针
 * @return ret_code_t 返回码
 */
ret_code_t battery_adc_get_info(battery_adc_info_t *info);

/**
 * @brief 测量原始电池电压
 * 
 * @param sample_count 采样次数
 * @return uint16_t 电池电压 (mV)，0表示测量失败
 */
uint16_t battery_adc_measure_raw(uint8_t sample_count);

/**
 * @brief 测量滤波后的电池电压
 * 
 * @return uint16_t 滤波后电池电压 (mV)
 */
uint16_t battery_adc_measure_filtered(void);

/**
 * @brief 电压转换为电量百分比
 * 
 * @param voltage_mv 电池电压 (mV)
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t battery_voltage_to_percent(uint16_t voltage_mv);

/**
 * @brief 根据电量百分比获取电池状态
 * 
 * @param percent 电量百分比
 * @return battery_status_t 电池状态
 */
battery_status_t battery_get_status_from_percent(uint8_t percent);

/**
 * @brief 电池状态转换为字符串
 * 
 * @param status 电池状态
 * @return const char* 状态字符串
 */
const char* battery_status_to_string(battery_status_t status);

// ==================== 状态检查接口 ====================

/**
 * @brief 检查电池是否为低电量
 * 
 * @return bool true表示低电量
 */
bool battery_is_low(void);

/**
 * @brief 检查电池是否为极低电量
 * 
 * @return bool true表示极低电量
 */
bool battery_is_critical(void);

/**
 * @brief 重置滤波器
 */
void battery_filter_reset(void);

// ==================== CW2015兼容接口 ====================

/**
 * @brief CW2015兼容初始化接口
 * 
 * @return ret_code_t 返回码
 */
ret_code_t cw2015_init(void);

/**
 * @brief CW2015兼容读取电量百分比接口
 * 
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t cw2015_read_soc_percent(void);

/**
 * @brief CW2015兼容读取电压接口
 * 
 * @return uint16_t 电池电压 (mV)
 */
uint16_t cw2015_read_voltage_mv(void);

/**
 * @brief CW2015兼容获取SOC接口
 * 
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t get_SOC(void);


/**
 * @brief CW2015兼容读取剩余运行时间接口
 * 
 * @return uint16_t 估算剩余时间 (分钟)
 */
uint16_t cw2015_read_rrt_min(void);

// ==================== 校准接口 ====================

/**
 * @brief 设置校准参数
 * 
 * @param offset_mv 偏移量 (mV)
 * @param gain_permille 增益 (千分比)
 */
void battery_set_calibration(int16_t offset_mv, uint16_t gain_permille);

/**
 * @brief 获取校准参数
 * 
 * @param offset_mv 偏移量指针
 * @param gain_permille 增益指针
 */
void battery_get_calibration(int16_t *offset_mv, uint16_t *gain_permille);

/**
 * @brief 执行ADC校准
 * 
 * @return ret_code_t 返回码
 */
ret_code_t battery_adc_calibrate(void);

/**
 * @brief 重置校准参数为默认值
 */
void battery_reset_calibration(void);

#endif // __BATTERY_ADC_NEW_H__
