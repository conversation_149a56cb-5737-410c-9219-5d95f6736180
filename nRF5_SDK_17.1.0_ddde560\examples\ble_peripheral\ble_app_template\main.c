/**
 * Copyright (c) 2014 - 2021, Nordic Semiconductor ASA
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Nordic
 *    Semiconductor ASA integrated circuit in a product or a software update for
 *    such product, must reproduce the above copyright notice, this list of
 *    conditions and the following disclaimer in the documentation and/or other
 *    materials provided with the distribution.
 *
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 *    contributors may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Nordic Semiconductor ASA integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
/** @file
 *
 * @defgroup ble_sdk_app_template_main main.c
 * @{
 * @ingroup ble_sdk_app_template
 * @brief Template project main file.
 *
 * This file contains a template for creating a new application. It has the code necessary to wakeup
 * from button, advertise, get a connection restart advertising on disconnect and if no new
 * connection created go back to system-off mode.
 * It can easily be used as a starting point for creating a new application, the comments identified
 * with 'YOUR_JOB' indicates where and how you can customize.
 */

#include <stdbool.h>
#include <stdint.h>
#include <string.h>

#include "nordic_common.h"
#include "nrf.h"
#include "app_error.h"
#include "ble.h"
#include "ble_hci.h"
#include "ble_srv_common.h"
#include "ble_advdata.h"
#include "ble_advertising.h"
#include "ble_conn_params.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"
#include "app_timer.h"
#include "fds.h"
#include "peer_manager.h"
#include "peer_manager_handler.h"
#include "bsp_btn_ble.h"
#include "sensorsim.h"
#include "ble_conn_state.h"
#include "nrf_ble_gatt.h"
#include "nrf_ble_qwr.h"
#include "nrf_pwr_mgmt.h"

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

//my include
#include "ble_conn.h"
#include "spi.h"
#include "ST7789.h"
#include "gui.h"
#include "app_scheduler.h"
#include "nrf_drv_clock.h"
#include "nrf_drv_gpiote.h"
#include "nrf_drv_lpcomp.h"
#include "lv_timer.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ble.h"
#include "nrf_sdh_soc.h"
#include "gui_guider.h"
#include "events_init.h"
#include "main.h"
#include "key.h"
#include "saadc.h"
#include "pwm.h"
#include "frame.h"
#include "power.h"
#include "spiflash20.h"
#include "algo.h"
#include "analog.h"
#include "sm_power_on.h"
#include "nrf_power.h"
#include "twiTMP.h"
#include "battery_adc.h"
#include "bm8563.h"

#include "ble_dis.h"
#include "ble_bas.h"

// BLE and UART includes
#include "ble_nus.h"
#include "app_uart.h"
#include "nrf_uart.h"


 // ȫ�ֱ��������ڳ�����־
 bool early_charge_detected = false;

/**@brief Function for the Timer initialization.
 *
 * @details Initializes the timer module. This creates and starts application timers.
 */
static void timers_init(void)
{
    // Initialize timer module.
    ret_code_t err_code = app_timer_init();
    APP_ERROR_CHECK(err_code);
}

/**@brief Function for initializing the nrf log module.
 */
static void log_init(void)
{
    ret_code_t err_code = NRF_LOG_INIT(NULL);
    APP_ERROR_CHECK(err_code);

    NRF_LOG_DEFAULT_BACKENDS_INIT();
}

/**@brief Function for handling the idle state (main loop).
 *
 * @details If there is no pending log operation, then sleep until next the next event occurs.
 */
static void idle_state_handle(void)
{
	nrf_pwr_mgmt_run();
}

static void gpiote_init(void)
{
    ret_code_t err_code;

    if (!nrf_drv_gpiote_is_init()) {
        err_code = nrf_drv_gpiote_init();
        APP_ERROR_CHECK(err_code);
    }
}

void check_cpu_clock(void)
{
    uint32_t hfclkstat = NRF_CLOCK->HFCLKSTAT;
    bool running = (hfclkstat & CLOCK_HFCLKSTAT_STATE_Msk) ? true : false;
    bool src_xtal = (hfclkstat & CLOCK_HFCLKSTAT_SRC_Msk) ? true : false;

    NRF_LOG_INFO("CPU Frequency: 64 MHz");
    if (running)
    {
        NRF_LOG_INFO("HFCLK source: %s", src_xtal ? "HFXO (external 32 MHz crystal)" : "HFINT (internal RC)");
    }
    else
    {
        NRF_LOG_INFO("HFCLK not running (using LFCLK only?)");
    }
}

/**@brief SYSMC电源控制函数
 */
void sysmc_init(void)
{
    // 配置P0.25为输出模式
    nrf_gpio_cfg_output(SYSMC);
    // 初始状态设置为供电状态(低电平)
    nrf_gpio_pin_clear(SYSMC);
    NRF_LOG_INFO("SYSMC initialized - Power ON");
}

void sysmc_power_on(void)
{
    // 设置SYSMC为0，系统供电
    nrf_gpio_pin_clear(SYSMC);
    NRF_LOG_INFO("SYSMC: Power ON");
}

void sysmc_power_off(void)
{
    // 设置SYSMC为1，系统断电
    nrf_gpio_pin_set(SYSMC);
    NRF_LOG_INFO("SYSMC: Power OFF");
}


/**@brief Function for application main entry.
 */
int main(void)
{
    ret_code_t ret;
	
	 // ���ڼ����״̬ - �ڳ�ʼ��ǰ����Ƿ����������������
     nrf_gpio_cfg_input(BQ24075_PG_PIN, NRF_GPIO_PIN_PULLUP);
     nrf_gpio_cfg_input(BQ24075_CHG_PIN, NRF_GPIO_PIN_PULLUP);

     // ��ʱȷ��GPIO�ȶ�
     nrf_delay_ms(10);

     bool chrg_pin = nrf_gpio_pin_read(BQ24075_PG_PIN);
     bool done_pin = nrf_gpio_pin_read(BQ24075_CHG_PIN);

     // �����״̬��CHRG=0(�����) �� DONE=0(����)
     early_charge_detected = (chrg_pin == 0) || (done_pin == 0);

    // 初始化SYSMC电源控制
    sysmc_init();

	// �����߼��ж�
     if (!early_charge_detected) {
         // ����������Ҫ����USER��3��
         wait_user_key_long_press();
         // 长按成功后确保系统供电
         sysmc_power_on();
     } else {
         // 检测到充电，直接开机供电
         sysmc_power_on();
         NRF_LOG_INFO("Early charge detected - Auto power on");
     }

    // Initialize.
    log_init();
    timers_init();
    gpiote_init();
	//�ⲿflash��ʼ��
	spi_flash_init();
	 //��ʼ��������
    cw2015_init();
    //��������
    ble_connect();
	//ʱ��оƬ��ʼ��
	BM8563Init();
    //��ʼ�� SAADC ģ�飬����ģ���ѹ����
    saadc_init();
    //��ʼ��ͨ��֡��Э��֡ģ�飬׼�����ݽ������
    frame_init();
    // ִ�� SAADC ƫ��У׼��������������
    saadc_calibrate_offset();
    algo_init();
    analog_init();
    APP_SCHED_INIT(APP_TIMER_SCHED_EVENT_DATA_SIZE, 20);
    //������IC��ʼ��
    bq24075_gpio_init();
    //�¶ȴ�������ʼ��
    tmp_init();
    //LCD��GUI init
    st7789_init();
    gui_init();
    //������ʼ��
    key_init();
    //̽ͷ�Ƴ�ʼ��
    pwm_led_init();
	ws2812_init();
    //״̬����ʼ��
    sm1_init();
	
	
	check_cpu_clock();
	 uint32_t last_lvgl_ticks = app_timer_cnt_get();
    // Enter main loop.
    for (;;) {
        while (NRF_LOG_PROCESS()) {}
        app_sched_execute();

        // LVGL ˢ�£�Լ 5ms һ��
        uint32_t now_ticks = app_timer_cnt_get();
        if (app_timer_cnt_diff_compute(now_ticks, last_lvgl_ticks) >= APP_TIMER_TICKS(5)) {
            lv_timer_handler();
            last_lvgl_ticks = now_ticks;
        }
		// �������״̬�仯
		bq24075_process_state_change();
        idle_state_handle();
		
    }
}


/**
 * @}
 */
